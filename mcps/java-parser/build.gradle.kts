plugins {
    alias(libs.plugins.kotlinJvm)
    alias(libs.plugins.kotlinSerialization)
    application
}

group = "com.phodal.archlift.mcps"
version = "1.0.0"

application {
    mainClass.set("com.phodal.archlift.mcps.javaparser.JavaParserServerKt")
}

dependencies {
    implementation(libs.mcp.kotlin.sdk)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.logback)
    
    // Java parser dependencies
    implementation("com.github.javaparser:javaparser-core:3.25.8")
    implementation("com.github.javaparser:javaparser-symbol-solver-core:3.25.8")
    
    testImplementation(libs.kotlin.testJunit)
}
