package com.phodal.archlift.mcps.javaparser

import com.github.javaparser.JavaParser
import com.github.javaparser.ast.CompilationUnit
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration
import com.github.javaparser.ast.body.MethodDeclaration
import com.github.javaparser.ast.body.FieldDeclaration
import com.github.javaparser.ast.body.ConstructorDeclaration
import com.github.javaparser.ast.visitor.VoidVisitorAdapter
import kotlinx.serialization.Serializable
import java.io.File

/**
 * Java 解析结果数据类
 */
@Serializable
data class JavaParseResult(
    val fileName: String,
    val packageName: String?,
    val imports: List<String>,
    val classes: List<JavaClassInfo>
)

@Serializable
data class JavaClassInfo(
    val name: String,
    val type: String, // "class", "interface", "enum", "annotation"
    val modifiers: List<String>,
    val superClass: String?,
    val interfaces: List<String>,
    val fields: List<JavaFieldInfo>,
    val methods: List<JavaMethodInfo>,
    val constructors: List<JavaConstructorInfo>,
    val innerClasses: List<JavaClassInfo>
)

@Serializable
data class JavaFieldInfo(
    val name: String,
    val type: String,
    val modifiers: List<String>,
    val initialValue: String?
)

@Serializable
data class JavaMethodInfo(
    val name: String,
    val returnType: String,
    val modifiers: List<String>,
    val parameters: List<JavaParameterInfo>,
    val exceptions: List<String>,
    val isAbstract: Boolean,
    val isStatic: Boolean
)

@Serializable
data class JavaConstructorInfo(
    val modifiers: List<String>,
    val parameters: List<JavaParameterInfo>,
    val exceptions: List<String>
)

@Serializable
data class JavaParameterInfo(
    val name: String,
    val type: String
)

@Serializable
data class JavaProjectAnalysis(
    val projectPath: String,
    val totalFiles: Int,
    val packages: List<JavaPackageInfo>,
    val summary: JavaProjectSummary
)

@Serializable
data class JavaPackageInfo(
    val name: String,
    val files: List<String>,
    val classes: List<String>
)

@Serializable
data class JavaProjectSummary(
    val totalClasses: Int,
    val totalMethods: Int,
    val totalFields: Int,
    val packageCount: Int
)

/**
 * 解析 Java 代码的核心函数
 */
fun parseJavaCodeInternal(code: String, fileName: String): JavaParseResult {
    val parser = JavaParser()
    val parseResult = parser.parse(code)
    
    if (!parseResult.isSuccessful) {
        throw RuntimeException("Failed to parse Java code: ${parseResult.problems}")
    }
    
    val cu = parseResult.result.get()
    val visitor = JavaStructureVisitor()
    visitor.visit(cu, null)
    
    return JavaParseResult(
        fileName = fileName,
        packageName = cu.packageDeclaration.map { it.nameAsString }.orElse(null),
        imports = cu.imports.map { it.nameAsString },
        classes = visitor.classes
    )
}

/**
 * 查找项目中的所有 Java 文件
 */
fun findJavaFiles(directory: File): List<File> {
    val javaFiles = mutableListOf<File>()
    
    fun searchRecursively(dir: File) {
        dir.listFiles()?.forEach { file ->
            when {
                file.isDirectory -> searchRecursively(file)
                file.isFile && file.extension == "java" -> javaFiles.add(file)
            }
        }
    }
    
    searchRecursively(directory)
    return javaFiles
}

/**
 * 分析 Java 项目结构
 */
fun analyzeJavaProjectInternal(javaFiles: List<File>): JavaProjectAnalysis {
    val packages = mutableMapOf<String, MutableList<String>>()
    val allClasses = mutableListOf<String>()
    var totalMethods = 0
    var totalFields = 0
    
    javaFiles.forEach { file ->
        try {
            val parseResult = parseJavaCodeInternal(file.readText(), file.name)
            val packageName = parseResult.packageName ?: "default"
            
            packages.computeIfAbsent(packageName) { mutableListOf() }.add(file.name)
            
            parseResult.classes.forEach { classInfo ->
                allClasses.add("${packageName}.${classInfo.name}")
                totalMethods += classInfo.methods.size + classInfo.constructors.size
                totalFields += classInfo.fields.size
            }
        } catch (e: Exception) {
            println("Warning: Failed to parse ${file.name}: ${e.message}")
        }
    }
    
    val packageInfos = packages.map { (name, files) ->
        val classNames = files.mapNotNull { fileName ->
            try {
                val file = javaFiles.find { it.name == fileName }
                file?.let {
                    val parseResult = parseJavaCodeInternal(it.readText(), fileName)
                    parseResult.classes.map { classInfo -> classInfo.name }
                }?.flatten()
            } catch (e: Exception) {
                emptyList()
            }
        }.flatten()
        
        JavaPackageInfo(
            name = name,
            files = files,
            classes = classNames
        )
    }
    
    return JavaProjectAnalysis(
        projectPath = javaFiles.firstOrNull()?.parent ?: "",
        totalFiles = javaFiles.size,
        packages = packageInfos,
        summary = JavaProjectSummary(
            totalClasses = allClasses.size,
            totalMethods = totalMethods,
            totalFields = totalFields,
            packageCount = packages.size
        )
    )
}

/**
 * Java AST 访问器，用于提取结构信息
 */
class JavaStructureVisitor : VoidVisitorAdapter<Void>() {
    val classes = mutableListOf<JavaClassInfo>()
    
    override fun visit(n: ClassOrInterfaceDeclaration, arg: Void?) {
        val classInfo = JavaClassInfo(
            name = n.nameAsString,
            type = if (n.isInterface) "interface" else "class",
            modifiers = n.modifiers.map { it.keyword.asString() },
            superClass = n.extendedTypes.firstOrNull()?.nameAsString,
            interfaces = n.implementedTypes.map { it.nameAsString },
            fields = extractFields(n),
            methods = extractMethods(n),
            constructors = extractConstructors(n),
            innerClasses = extractInnerClasses(n)
        )
        
        classes.add(classInfo)
        super.visit(n, arg)
    }
    
    private fun extractFields(n: ClassOrInterfaceDeclaration): List<JavaFieldInfo> {
        return n.fields.flatMap { field ->
            field.variables.map { variable ->
                JavaFieldInfo(
                    name = variable.nameAsString,
                    type = field.elementType.asString(),
                    modifiers = field.modifiers.map { it.keyword.asString() },
                    initialValue = variable.initializer.map { it.toString() }.orElse(null)
                )
            }
        }
    }
    
    private fun extractMethods(n: ClassOrInterfaceDeclaration): List<JavaMethodInfo> {
        return n.methods.map { method ->
            JavaMethodInfo(
                name = method.nameAsString,
                returnType = method.type.asString(),
                modifiers = method.modifiers.map { it.keyword.asString() },
                parameters = method.parameters.map { param ->
                    JavaParameterInfo(
                        name = param.nameAsString,
                        type = param.type.asString()
                    )
                },
                exceptions = method.thrownExceptions.map { it.asString() },
                isAbstract = method.isAbstract,
                isStatic = method.isStatic
            )
        }
    }
    
    private fun extractConstructors(n: ClassOrInterfaceDeclaration): List<JavaConstructorInfo> {
        return n.constructors.map { constructor ->
            JavaConstructorInfo(
                modifiers = constructor.modifiers.map { it.keyword.asString() },
                parameters = constructor.parameters.map { param ->
                    JavaParameterInfo(
                        name = param.nameAsString,
                        type = param.type.asString()
                    )
                },
                exceptions = constructor.thrownExceptions.map { it.asString() }
            )
        }
    }
    
    private fun extractInnerClasses(n: ClassOrInterfaceDeclaration): List<JavaClassInfo> {
        val innerClasses = mutableListOf<JavaClassInfo>()
        n.members.forEach { member ->
            if (member is ClassOrInterfaceDeclaration) {
                val visitor = JavaStructureVisitor()
                visitor.visit(member, null)
                innerClasses.addAll(visitor.classes)
            }
        }
        return innerClasses
    }
}
