package com.phodal.archlift.mcps.javaparser

import com.github.javaparser.JavaParser
import com.github.javaparser.ast.CompilationUnit
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration
import com.github.javaparser.ast.body.MethodDeclaration
import com.github.javaparser.ast.visitor.VoidVisitorAdapter
import io.modelcontextprotocol.kotlin.sdk.*
import io.modelcontextprotocol.kotlin.sdk.server.Server
import io.modelcontextprotocol.kotlin.sdk.server.ServerOptions
import io.modelcontextprotocol.kotlin.sdk.server.StdioServerTransport
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths

/**
 * Java Parser MCP Server
 * 提供 Java 代码解析功能的 MCP 服务器
 */
suspend fun main() {
    val server = createJavaParserServer()
    val transport = StdioServerTransport()
    
    try {
        server.connect(transport)
        println("Java Parser MCP Server started successfully")
    } catch (e: Exception) {
        println("Failed to start Java Parser MCP Server: ${e.message}")
        e.printStackTrace()
    }
}

/**
 * 创建 Java Parser MCP Server
 */
fun createJavaParserServer(): Server {
    val server = Server(
        serverInfo = Implementation(
            name = "java-parser-server",
            version = "1.0.0"
        ),
        options = ServerOptions(
            capabilities = ServerCapabilities(
                resources = ServerCapabilities.Resources(
                    subscribe = true,
                    listChanged = true
                ),
                tools = ServerCapabilities.Tools(
                    listChanged = true
                )
            )
        )
    )

    // 添加资源：Java 文件解析
    server.addResource(
        uri = "java://parse",
        name = "Java Code Parser",
        description = "Parse Java source code and extract structure information",
        mimeType = "application/json"
    ) { request ->
        ReadResourceResult(
            contents = listOf(
                TextResourceContents(
                    text = "Java Parser Resource - Use tools to parse Java code",
                    uri = request.uri,
                    mimeType = "text/plain"
                )
            )
        )
    }

    // 添加工具：解析 Java 文件
    server.addTool(
        name = "parse_java_file",
        description = "Parse a Java file and extract class and method information",
        inputSchema = mapOf(
            "type" to "object",
            "properties" to mapOf(
                "file_path" to mapOf(
                    "type" to "string",
                    "description" to "Path to the Java file to parse"
                )
            ),
            "required" to listOf("file_path")
        )
    ) { request ->
        parseJavaFile(request)
    }

    // 添加工具：解析 Java 代码字符串
    server.addTool(
        name = "parse_java_code",
        description = "Parse Java code string and extract structure information",
        inputSchema = mapOf(
            "type" to "object",
            "properties" to mapOf(
                "code" to mapOf(
                    "type" to "string",
                    "description" to "Java source code to parse"
                )
            ),
            "required" to listOf("code")
        )
    ) { request ->
        parseJavaCode(request)
    }

    // 添加工具：分析 Java 项目结构
    server.addTool(
        name = "analyze_java_project",
        description = "Analyze Java project structure and extract package/class hierarchy",
        inputSchema = mapOf(
            "type" to "object",
            "properties" to mapOf(
                "project_path" to mapOf(
                    "type" to "string",
                    "description" to "Path to the Java project root directory"
                )
            ),
            "required" to listOf("project_path")
        )
    ) { request ->
        analyzeJavaProject(request)
    }

    return server
}

/**
 * 解析 Java 文件
 */
private fun parseJavaFile(request: CallToolRequest): CallToolResult {
    return try {
        val filePath = request.arguments["file_path"] as? String
            ?: return CallToolResult(
                content = listOf(
                    TextContent(
                        type = "text",
                        text = "Error: file_path parameter is required"
                    )
                ),
                isError = true
            )

        val file = File(filePath)
        if (!file.exists() || !file.isFile) {
            return CallToolResult(
                content = listOf(
                    TextContent(
                        type = "text",
                        text = "Error: File not found or is not a file: $filePath"
                    )
                ),
                isError = true
            )
        }

        val javaCode = file.readText()
        val parseResult = parseJavaCodeInternal(javaCode, filePath)
        
        CallToolResult(
            content = listOf(
                TextContent(
                    type = "text",
                    text = Json.encodeToString(JavaParseResult.serializer(), parseResult)
                )
            )
        )
    } catch (e: Exception) {
        CallToolResult(
            content = listOf(
                TextContent(
                    type = "text",
                    text = "Error parsing Java file: ${e.message}"
                )
            ),
            isError = true
        )
    }
}

/**
 * 解析 Java 代码字符串
 */
private fun parseJavaCode(request: CallToolRequest): CallToolResult {
    return try {
        val code = request.arguments["code"] as? String
            ?: return CallToolResult(
                content = listOf(
                    TextContent(
                        type = "text",
                        text = "Error: code parameter is required"
                    )
                ),
                isError = true
            )

        val parseResult = parseJavaCodeInternal(code, "inline-code")
        
        CallToolResult(
            content = listOf(
                TextContent(
                    type = "text",
                    text = Json.encodeToString(JavaParseResult.serializer(), parseResult)
                )
            )
        )
    } catch (e: Exception) {
        CallToolResult(
            content = listOf(
                TextContent(
                    type = "text",
                    text = "Error parsing Java code: ${e.message}"
                )
            ),
            isError = true
        )
    }
}

/**
 * 分析 Java 项目结构
 */
private fun analyzeJavaProject(request: CallToolRequest): CallToolResult {
    return try {
        val projectPath = request.arguments["project_path"] as? String
            ?: return CallToolResult(
                content = listOf(
                    TextContent(
                        type = "text",
                        text = "Error: project_path parameter is required"
                    )
                ),
                isError = true
            )

        val projectDir = File(projectPath)
        if (!projectDir.exists() || !projectDir.isDirectory) {
            return CallToolResult(
                content = listOf(
                    TextContent(
                        type = "text",
                        text = "Error: Project directory not found: $projectPath"
                    )
                ),
                isError = true
            )
        }

        val javaFiles = findJavaFiles(projectDir)
        val projectAnalysis = analyzeJavaProjectInternal(javaFiles)
        
        CallToolResult(
            content = listOf(
                TextContent(
                    type = "text",
                    text = Json.encodeToString(JavaProjectAnalysis.serializer(), projectAnalysis)
                )
            )
        )
    } catch (e: Exception) {
        CallToolResult(
            content = listOf(
                TextContent(
                    type = "text",
                    text = "Error analyzing Java project: ${e.message}"
                )
            ),
            isError = true
        )
    }
}
