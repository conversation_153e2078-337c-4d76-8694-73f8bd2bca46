package com.example.test;

import java.util.List;
import java.util.ArrayList;

/**
 * Example Java class for testing the parser
 */
public class Example {
    private String name;
    private int age;
    private static final String CONSTANT = "test";
    
    public Example() {
        this.name = "default";
        this.age = 0;
    }
    
    public Example(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public int getAge() {
        return age;
    }
    
    public void setAge(int age) {
        this.age = age;
    }
    
    public static String getConstant() {
        return CONSTANT;
    }
    
    @Override
    public String toString() {
        return "Example{name='" + name + "', age=" + age + "}";
    }
    
    public interface InnerInterface {
        void doSomething();
    }
    
    public static class InnerClass implements InnerInterface {
        @Override
        public void doSomething() {
            System.out.println("Doing something...");
        }
    }
}
