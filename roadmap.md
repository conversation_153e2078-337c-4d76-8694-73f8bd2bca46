# ArchLift

ArchLift 是基于最新开放标准 MCP（Model Context Protocol） 构建的一站式现代化工具，将传统遗留系统无缝融入 AI 与云时代。
底层采用 Kotlin MPP，前端使用 Compose UI，后端以 Ktor 驱动，并植入丰富扩展——AST 解析、代码生成、构建系统支持、数据库解析、
自动修复与 AI Agent 构建等模块，打造从代码理解到自动迁移的全流程智能现代化引擎。

MCP extensions:

- [ ] AST Transformer
    - JSP code-parser
    - React code-parser
- [ ] Common infra
    - [ ] Swagger/OpenAPI
    - [ ] SCC
- [ ] Build System
    - Maven [Aether](https://maven.apache.org/resolver/)
    - Gradle [Tooling API](https://docs.gradle.org/current/userguide/tooling_api.html)
- [ ] Code generator
    - JavaPoet, KotlinPoet
    - [ts-morph](https://github.com/dsherret/ts-morph)
    - [Recast](https://github.com/benjamn/recast)
- [ ] Decompiler
    - Java
        - ~~FernFlower~~
        - ~~https://github.com/leibnitz27/cfr~~
        - [Vineflower](https://github.com/Vineflower/vineflower) is a modern, general purpose JVM language decompiler
          focused on providing the best quality, speed, and usability.
    - .Net
        - https://github.com/dnSpyEx/dnSpy
        - https://github.com/icsharpcode/ILSpy
    - C++ LLVM
        - https://github.com/avast/retdec
    - Python
        - https://github.com/rocky/python-uncompyle6 
- [ ] Database
    - [ ] Python [SQLParse](https://github.com/andialbrecht/sqlparse)
    - [ ] [JSqlParser](https://github.com/JSQLParser/JSqlParser)
- [ ] Runtime Fixer
    - Auto frontend fix with Playwright
- [ ] Build Fixer
    - Auto build fix with Process mananger
- [ ] AI Agent builder 
