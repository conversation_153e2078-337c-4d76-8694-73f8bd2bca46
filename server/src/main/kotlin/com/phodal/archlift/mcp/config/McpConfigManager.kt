package com.phodal.archlift.mcp.config

import kotlinx.serialization.json.Json
import java.io.File
import java.nio.file.Path
import java.nio.file.Paths
import kotlin.io.path.exists
import kotlin.io.path.readText

/**
 * MCP 配置管理器
 * 负责读取、解析和管理 MCP 配置文件
 */
class McpConfigManager(
    private val configPath: Path = Paths.get("mcp-config.json")
) {
    private val json = Json {
        ignoreUnknownKeys = true
        prettyPrint = true
    }

    private var _config: McpConfig? = null
    private val serverInstances = mutableMapOf<String, McpServerInstance>()

    /**
     * 获取当前配置
     */
    val config: McpConfig?
        get() = _config

    /**
     * 获取所有服务器实例
     */
    val servers: Map<String, McpServerInstance>
        get() = serverInstances.toMap()

    /**
     * 加载配置文件
     */
    fun loadConfig(): McpConfig? {
        return try {
            if (!configPath.exists()) {
                createDefaultConfig()
                return null
            }

            val configText = configPath.readText()
            val config = json.decodeFromString<McpConfig>(configText)
            _config = config

            // 更新服务器实例
            updateServerInstances(config)
            
            config
        } catch (e: Exception) {
            println("Failed to load MCP config: ${e.message}")
            null
        }
    }

    /**
     * 重新加载配置
     */
    fun reloadConfig(): McpConfig? {
        return loadConfig()
    }

    /**
     * 获取指定名称的服务器实例
     */
    fun getServer(name: String): McpServerInstance? {
        return serverInstances[name]
    }

    /**
     * 获取所有启用的服务器
     */
    fun getEnabledServers(): List<McpServerInstance> {
        return serverInstances.values.filter { !it.config.disabled }
    }

    /**
     * 更新服务器状态
     */
    fun updateServerStatus(name: String, status: McpServerStatus, error: String? = null) {
        serverInstances[name]?.let { instance ->
            instance.status = status
            instance.lastError = error
            if (status == McpServerStatus.RUNNING && instance.startTime == null) {
                instance.startTime = System.currentTimeMillis()
            } else if (status == McpServerStatus.STOPPED) {
                instance.startTime = null
                instance.process = null
            }
        }
    }

    /**
     * 设置服务器进程
     */
    fun setServerProcess(name: String, process: Process) {
        serverInstances[name]?.process = process
    }

    /**
     * 创建默认配置文件
     */
    private fun createDefaultConfig() {
        val defaultConfig = McpConfig(
            mcpServers = mapOf(
                "example-server" to McpServerConfig(
                    command = "node",
                    args = listOf("server.js"),
                    disabled = true,
                    autoApprove = emptyList()
                )
            )
        )

        try {
            val configText = json.encodeToString(McpConfig.serializer(), defaultConfig)
            configPath.toFile().writeText(configText)
            println("Created default MCP config at: $configPath")
        } catch (e: Exception) {
            println("Failed to create default config: ${e.message}")
        }
    }

    /**
     * 更新服务器实例
     */
    private fun updateServerInstances(config: McpConfig) {
        // 移除不再存在的服务器
        val configServerNames = config.mcpServers.keys
        serverInstances.keys.removeAll { it !in configServerNames }

        // 添加或更新服务器实例
        config.mcpServers.forEach { (name, serverConfig) ->
            val existingInstance = serverInstances[name]
            if (existingInstance != null) {
                // 更新现有实例的配置
                serverInstances[name] = existingInstance.copy(config = serverConfig)
            } else {
                // 创建新实例
                serverInstances[name] = McpServerInstance(
                    name = name,
                    config = serverConfig,
                    status = if (serverConfig.disabled) McpServerStatus.DISABLED else McpServerStatus.STOPPED
                )
            }
        }
    }
}
