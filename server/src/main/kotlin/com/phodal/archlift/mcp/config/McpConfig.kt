package com.phodal.archlift.mcp.config

import kotlinx.serialization.Serializable

/**
 * MCP 配置文件的根结构
 */
@Serializable
data class McpConfig(
    val mcpServers: Map<String, McpServerConfig>
)

/**
 * 单个 MCP Server 的配置
 */
@Serializable
data class McpServerConfig(
    val command: String,
    val args: List<String> = emptyList(),
    val env: Map<String, String> = emptyMap(),
    val disabled: Boolean = false,
    val autoApprove: List<String> = emptyList(),
    val workingDirectory: String? = null,
    val timeout: Long = 30000L // 30 seconds default timeout
)

/**
 * MCP Server 运行时状态
 */
enum class McpServerStatus {
    STOPPED,
    STARTING,
    RUNNING,
    ERROR,
    DISABLED
}

/**
 * MCP Server 运行时信息
 */
data class McpServerInstance(
    val name: String,
    val config: McpServerConfig,
    var status: McpServerStatus = McpServerStatus.STOPPED,
    var process: Process? = null,
    var lastError: String? = null,
    var startTime: Long? = null
)
