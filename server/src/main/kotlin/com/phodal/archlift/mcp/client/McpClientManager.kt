package com.phodal.archlift.mcp.client

import com.phodal.archlift.mcp.config.McpConfigManager
import com.phodal.archlift.mcp.config.McpServerInstance
import com.phodal.archlift.mcp.config.McpServerStatus
import io.modelcontextprotocol.kotlin.sdk.client.Client
import io.modelcontextprotocol.kotlin.sdk.client.StdioClientTransport
import io.modelcontextprotocol.kotlin.sdk.Implementation
import kotlinx.coroutines.*
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * MCP Client 管理器
 * 负责管理与多个 MCP Server 的连接
 */
class McpClientManager(
    private val configManager: McpConfigManager
) {
    private val clients = ConcurrentHashMap<String, Client>()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * 启动所有启用的 MCP 服务器
     */
    suspend fun startAllServers() {
        val enabledServers = configManager.getEnabledServers()
        enabledServers.forEach { server ->
            startServer(server)
        }
    }

    /**
     * 启动指定的 MCP 服务器
     */
    suspend fun startServer(serverInstance: McpServerInstance) {
        if (serverInstance.config.disabled) {
            configManager.updateServerStatus(serverInstance.name, McpServerStatus.DISABLED)
            return
        }

        try {
            configManager.updateServerStatus(serverInstance.name, McpServerStatus.STARTING)

            // 创建进程
            val process = createServerProcess(serverInstance)
            configManager.setServerProcess(serverInstance.name, process)

            // 创建 MCP Client
            val client = Client(
                clientInfo = Implementation(
                    name = "archlift-client",
                    version = "1.0.0"
                )
            )

            // 创建 STDIO 传输
            val transport = StdioClientTransport(
                inputStream = process.inputStream,
                outputStream = process.outputStream
            )

            // 连接到服务器
            client.connect(transport)
            clients[serverInstance.name] = client

            configManager.updateServerStatus(serverInstance.name, McpServerStatus.RUNNING)
            println("Successfully started MCP server: ${serverInstance.name}")

        } catch (e: Exception) {
            configManager.updateServerStatus(
                serverInstance.name, 
                McpServerStatus.ERROR, 
                e.message
            )
            println("Failed to start MCP server ${serverInstance.name}: ${e.message}")
        }
    }

    /**
     * 停止指定的 MCP 服务器
     */
    suspend fun stopServer(serverName: String) {
        val client = clients.remove(serverName)
        client?.disconnect()

        val serverInstance = configManager.getServer(serverName)
        serverInstance?.process?.let { process ->
            try {
                process.destroy()
                if (!process.waitFor(5, java.util.concurrent.TimeUnit.SECONDS)) {
                    process.destroyForcibly()
                }
            } catch (e: Exception) {
                println("Error stopping server process $serverName: ${e.message}")
            }
        }

        configManager.updateServerStatus(serverName, McpServerStatus.STOPPED)
        println("Stopped MCP server: $serverName")
    }

    /**
     * 停止所有 MCP 服务器
     */
    suspend fun stopAllServers() {
        val serverNames = clients.keys.toList()
        serverNames.forEach { serverName ->
            stopServer(serverName)
        }
    }

    /**
     * 获取指定服务器的客户端
     */
    fun getClient(serverName: String): Client? {
        return clients[serverName]
    }

    /**
     * 获取所有活跃的客户端
     */
    fun getAllClients(): Map<String, Client> {
        return clients.toMap()
    }

    /**
     * 重新加载配置并重启服务器
     */
    suspend fun reloadConfiguration() {
        // 停止所有现有服务器
        stopAllServers()

        // 重新加载配置
        configManager.reloadConfig()

        // 启动新配置的服务器
        startAllServers()
    }

    /**
     * 创建服务器进程
     */
    private fun createServerProcess(serverInstance: McpServerInstance): Process {
        val config = serverInstance.config
        val processBuilder = ProcessBuilder()

        // 设置命令和参数
        val command = mutableListOf(config.command)
        command.addAll(config.args)
        processBuilder.command(command)

        // 设置环境变量
        if (config.env.isNotEmpty()) {
            val environment = processBuilder.environment()
            environment.putAll(config.env)
        }

        // 设置工作目录
        config.workingDirectory?.let { workDir ->
            processBuilder.directory(File(workDir))
        }

        // 重定向错误流
        processBuilder.redirectErrorStream(true)

        return processBuilder.start()
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        runBlocking {
            stopAllServers()
        }
    }
}
