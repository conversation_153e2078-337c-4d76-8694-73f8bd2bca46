package com.phodal.archlift.mcp.client

import io.modelcontextprotocol.kotlin.sdk.*
import kotlinx.serialization.Serializable

/**
 * MCP 工具调用服务
 * 提供对 MCP 服务器工具的调用功能
 */
class McpToolService(
    private val clientManager: McpClientManager
) {

    /**
     * 列出指定服务器的所有工具
     */
    suspend fun listTools(serverName: String): ToolListResponse {
        val client = clientManager.getClient(serverName)
            ?: return ToolListResponse(false, "Server not connected: $serverName", emptyList())

        return try {
            val tools = client.listTools(ListToolsRequest())
            ToolListResponse(
                success = true,
                message = "Tools listed successfully",
                tools = tools.tools.map { tool ->
                    ToolInfo(
                        name = tool.name,
                        description = tool.description ?: "",
                        inputSchema = tool.inputSchema
                    )
                }
            )
        } catch (e: Exception) {
            ToolListResponse(false, "Failed to list tools: ${e.message}", emptyList())
        }
    }

    /**
     * 调用指定服务器的工具
     */
    suspend fun callTool(
        serverName: String, 
        toolName: String, 
        arguments: Map<String, Any>
    ): ToolCallResponse {
        val client = clientManager.getClient(serverName)
            ?: return ToolCallResponse(false, "Server not connected: $serverName", null)

        return try {
            val result = client.callTool(
                CallToolRequest(
                    name = toolName,
                    arguments = arguments
                )
            )
            
            ToolCallResponse(
                success = true,
                message = "Tool called successfully",
                result = ToolCallResult(
                    content = result.content.map { content ->
                        when (content) {
                            is TextContent -> ToolContent(
                                type = "text",
                                text = content.text
                            )
                            is ImageContent -> ToolContent(
                                type = "image",
                                text = content.data
                            )
                            else -> ToolContent(
                                type = "unknown",
                                text = content.toString()
                            )
                        }
                    },
                    isError = result.isError ?: false
                )
            )
        } catch (e: Exception) {
            ToolCallResponse(false, "Failed to call tool: ${e.message}", null)
        }
    }

    /**
     * 列出指定服务器的所有提示
     */
    suspend fun listPrompts(serverName: String): PromptListResponse {
        val client = clientManager.getClient(serverName)
            ?: return PromptListResponse(false, "Server not connected: $serverName", emptyList())

        return try {
            val prompts = client.listPrompts(ListPromptsRequest())
            PromptListResponse(
                success = true,
                message = "Prompts listed successfully",
                prompts = prompts.prompts.map { prompt ->
                    PromptInfo(
                        name = prompt.name,
                        description = prompt.description ?: "",
                        arguments = prompt.arguments?.map { arg ->
                            PromptArgument(
                                name = arg.name,
                                description = arg.description ?: "",
                                required = arg.required ?: false
                            )
                        } ?: emptyList()
                    )
                }
            )
        } catch (e: Exception) {
            PromptListResponse(false, "Failed to list prompts: ${e.message}", emptyList())
        }
    }

    /**
     * 获取指定提示
     */
    suspend fun getPrompt(
        serverName: String,
        promptName: String,
        arguments: Map<String, String> = emptyMap()
    ): PromptResponse {
        val client = clientManager.getClient(serverName)
            ?: return PromptResponse(false, "Server not connected: $serverName", null)

        return try {
            val result = client.getPrompt(
                GetPromptRequest(
                    name = promptName,
                    arguments = arguments
                )
            )
            
            PromptResponse(
                success = true,
                message = "Prompt retrieved successfully",
                prompt = PromptResult(
                    description = result.description ?: "",
                    messages = result.messages.map { message ->
                        PromptMessage(
                            role = message.role.name.lowercase(),
                            content = when (val content = message.content) {
                                is TextContent -> content.text
                                is ImageContent -> content.data
                                else -> content.toString()
                            }
                        )
                    }
                )
            )
        } catch (e: Exception) {
            PromptResponse(false, "Failed to get prompt: ${e.message}", null)
        }
    }
}

// 数据类定义
@Serializable
data class ToolListResponse(
    val success: Boolean,
    val message: String,
    val tools: List<ToolInfo>
)

@Serializable
data class ToolInfo(
    val name: String,
    val description: String,
    val inputSchema: Map<String, Any>
)

@Serializable
data class ToolCallResponse(
    val success: Boolean,
    val message: String,
    val result: ToolCallResult?
)

@Serializable
data class ToolCallResult(
    val content: List<ToolContent>,
    val isError: Boolean
)

@Serializable
data class ToolContent(
    val type: String,
    val text: String
)

@Serializable
data class PromptListResponse(
    val success: Boolean,
    val message: String,
    val prompts: List<PromptInfo>
)

@Serializable
data class PromptInfo(
    val name: String,
    val description: String,
    val arguments: List<PromptArgument>
)

@Serializable
data class PromptArgument(
    val name: String,
    val description: String,
    val required: Boolean
)

@Serializable
data class PromptResponse(
    val success: Boolean,
    val message: String,
    val prompt: PromptResult?
)

@Serializable
data class PromptResult(
    val description: String,
    val messages: List<PromptMessage>
)

@Serializable
data class PromptMessage(
    val role: String,
    val content: String
)
