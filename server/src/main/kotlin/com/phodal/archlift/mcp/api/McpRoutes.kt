package com.phodal.archlift.mcp.api

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable

/**
 * MCP API 路由定义
 */
fun Routing.mcpRoutes(apiService: McpApiService) {
    route("/api/mcp") {
        
        // 获取所有服务器状态
        get("/servers") {
            val statuses = apiService.getServerStatuses()
            call.respond(HttpStatusCode.OK, statuses)
        }

        // 获取指定服务器状态
        get("/servers/{name}") {
            val serverName = call.parameters["name"] ?: return@get call.respond(
                HttpStatusCode.BadRequest, 
                ApiResponse(false, "Server name is required")
            )
            
            val status = apiService.getServerStatus(serverName)
            if (status != null) {
                call.respond(HttpStatusCode.OK, status)
            } else {
                call.respond(
                    HttpStatusCode.NotFound, 
                    ApiResponse(false, "Server not found: $serverName")
                )
            }
        }

        // 启动服务器
        post("/servers/{name}/start") {
            val serverName = call.parameters["name"] ?: return@post call.respond(
                HttpStatusCode.BadRequest, 
                ApiResponse(false, "Server name is required")
            )
            
            val response = apiService.startServer(serverName)
            val statusCode = if (response.success) HttpStatusCode.OK else HttpStatusCode.InternalServerError
            call.respond(statusCode, response)
        }

        // 停止服务器
        post("/servers/{name}/stop") {
            val serverName = call.parameters["name"] ?: return@post call.respond(
                HttpStatusCode.BadRequest, 
                ApiResponse(false, "Server name is required")
            )
            
            val response = apiService.stopServer(serverName)
            val statusCode = if (response.success) HttpStatusCode.OK else HttpStatusCode.InternalServerError
            call.respond(statusCode, response)
        }

        // 重新加载配置
        post("/config/reload") {
            val response = apiService.reloadConfig()
            val statusCode = if (response.success) HttpStatusCode.OK else HttpStatusCode.InternalServerError
            call.respond(statusCode, response)
        }

        // 列出服务器资源
        get("/servers/{name}/resources") {
            val serverName = call.parameters["name"] ?: return@get call.respond(
                HttpStatusCode.BadRequest, 
                ApiResponse(false, "Server name is required")
            )
            
            val response = apiService.listResources(serverName)
            val statusCode = if (response.success) HttpStatusCode.OK else HttpStatusCode.InternalServerError
            call.respond(statusCode, response)
        }

        // 读取指定资源
        get("/servers/{name}/resources/{uri}") {
            val serverName = call.parameters["name"] ?: return@get call.respond(
                HttpStatusCode.BadRequest, 
                ApiResponse(false, "Server name is required")
            )
            
            val resourceUri = call.parameters["uri"] ?: return@get call.respond(
                HttpStatusCode.BadRequest, 
                ApiResponse(false, "Resource URI is required")
            )
            
            val response = apiService.readResource(serverName, resourceUri)
            val statusCode = if (response.success) HttpStatusCode.OK else HttpStatusCode.InternalServerError
            call.respond(statusCode, response)
        }

        // 列出服务器工具
        get("/servers/{name}/tools") {
            val serverName = call.parameters["name"] ?: return@get call.respond(
                HttpStatusCode.BadRequest,
                ApiResponse(false, "Server name is required")
            )

            val response = apiService.listTools(serverName)
            val statusCode = if (response.success) HttpStatusCode.OK else HttpStatusCode.InternalServerError
            call.respond(statusCode, response)
        }

        // 调用服务器工具
        post("/servers/{name}/tools/{toolName}") {
            val serverName = call.parameters["name"] ?: return@post call.respond(
                HttpStatusCode.BadRequest,
                ApiResponse(false, "Server name is required")
            )

            val toolName = call.parameters["toolName"] ?: return@post call.respond(
                HttpStatusCode.BadRequest,
                ApiResponse(false, "Tool name is required")
            )

            val request = call.receive<ToolCallRequest>()
            val response = apiService.callTool(serverName, toolName, request.arguments)
            val statusCode = if (response.success) HttpStatusCode.OK else HttpStatusCode.InternalServerError
            call.respond(statusCode, response)
        }

        // 列出服务器提示
        get("/servers/{name}/prompts") {
            val serverName = call.parameters["name"] ?: return@get call.respond(
                HttpStatusCode.BadRequest,
                ApiResponse(false, "Server name is required")
            )

            val response = apiService.listPrompts(serverName)
            val statusCode = if (response.success) HttpStatusCode.OK else HttpStatusCode.InternalServerError
            call.respond(statusCode, response)
        }

        // 获取指定提示
        post("/servers/{name}/prompts/{promptName}") {
            val serverName = call.parameters["name"] ?: return@post call.respond(
                HttpStatusCode.BadRequest,
                ApiResponse(false, "Server name is required")
            )

            val promptName = call.parameters["promptName"] ?: return@post call.respond(
                HttpStatusCode.BadRequest,
                ApiResponse(false, "Prompt name is required")
            )

            val request = call.receive<PromptRequest>()
            val response = apiService.getPrompt(serverName, promptName, request.arguments)
            val statusCode = if (response.success) HttpStatusCode.OK else HttpStatusCode.InternalServerError
            call.respond(statusCode, response)
        }

        // 健康检查
        get("/health") {
            call.respond(HttpStatusCode.OK, ApiResponse(true, "MCP Client is running"))
        }
    }
}

@Serializable
data class ToolCallRequest(
    val arguments: Map<String, Any>
)

@Serializable
data class PromptRequest(
    val arguments: Map<String, String> = emptyMap()
)
