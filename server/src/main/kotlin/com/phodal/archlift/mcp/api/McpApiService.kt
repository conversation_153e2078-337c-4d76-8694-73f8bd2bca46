package com.phodal.archlift.mcp.api

import com.phodal.archlift.mcp.client.McpClientManager
import com.phodal.archlift.mcp.client.McpToolService
import com.phodal.archlift.mcp.config.McpConfigManager
import com.phodal.archlift.mcp.config.McpServerInstance
import io.modelcontextprotocol.kotlin.sdk.ListResourcesRequest
import io.modelcontextprotocol.kotlin.sdk.ReadResourceRequest
import kotlinx.serialization.Serializable

/**
 * MCP API 服务
 * 提供 REST API 接口来管理和操作 MCP 服务器
 */
class McpApiService(
    private val configManager: McpConfigManager,
    private val clientManager: McpClientManager,
    private val toolService: McpToolService
) {

    /**
     * 获取所有服务器状态
     */
    fun getServerStatuses(): List<ServerStatusResponse> {
        return configManager.servers.values.map { instance ->
            ServerStatusResponse(
                name = instance.name,
                status = instance.status.name,
                command = instance.config.command,
                args = instance.config.args,
                disabled = instance.config.disabled,
                lastError = instance.lastError,
                startTime = instance.startTime
            )
        }
    }

    /**
     * 获取指定服务器状态
     */
    fun getServerStatus(serverName: String): ServerStatusResponse? {
        val instance = configManager.getServer(serverName) ?: return null
        return ServerStatusResponse(
            name = instance.name,
            status = instance.status.name,
            command = instance.config.command,
            args = instance.config.args,
            disabled = instance.config.disabled,
            lastError = instance.lastError,
            startTime = instance.startTime
        )
    }

    /**
     * 启动服务器
     */
    suspend fun startServer(serverName: String): ApiResponse {
        val serverInstance = configManager.getServer(serverName)
            ?: return ApiResponse(false, "Server not found: $serverName")

        return try {
            clientManager.startServer(serverInstance)
            ApiResponse(true, "Server started successfully")
        } catch (e: Exception) {
            ApiResponse(false, "Failed to start server: ${e.message}")
        }
    }

    /**
     * 停止服务器
     */
    suspend fun stopServer(serverName: String): ApiResponse {
        return try {
            clientManager.stopServer(serverName)
            ApiResponse(true, "Server stopped successfully")
        } catch (e: Exception) {
            ApiResponse(false, "Failed to stop server: ${e.message}")
        }
    }

    /**
     * 重新加载配置
     */
    suspend fun reloadConfig(): ApiResponse {
        return try {
            clientManager.reloadConfiguration()
            ApiResponse(true, "Configuration reloaded successfully")
        } catch (e: Exception) {
            ApiResponse(false, "Failed to reload configuration: ${e.message}")
        }
    }

    /**
     * 列出指定服务器的资源
     */
    suspend fun listResources(serverName: String): ResourceListResponse {
        val client = clientManager.getClient(serverName)
            ?: return ResourceListResponse(false, "Server not connected: $serverName", emptyList())

        return try {
            val resources = client.listResources(ListResourcesRequest())
            ResourceListResponse(
                success = true,
                message = "Resources listed successfully",
                resources = resources.resources.map { resource ->
                    ResourceInfo(
                        uri = resource.uri,
                        name = resource.name ?: "",
                        description = resource.description,
                        mimeType = resource.mimeType
                    )
                }
            )
        } catch (e: Exception) {
            ResourceListResponse(false, "Failed to list resources: ${e.message}", emptyList())
        }
    }

    /**
     * 读取指定资源
     */
    suspend fun readResource(serverName: String, resourceUri: String): ResourceContentResponse {
        val client = clientManager.getClient(serverName)
            ?: return ResourceContentResponse(false, "Server not connected: $serverName", null)

        return try {
            val result = client.readResource(ReadResourceRequest(uri = resourceUri))
            val content = result.contents.firstOrNull()
            ResourceContentResponse(
                success = true,
                message = "Resource read successfully",
                content = content?.let {
                    when (it) {
                        is io.modelcontextprotocol.kotlin.sdk.TextResourceContents -> 
                            ResourceContent(
                                type = "text",
                                text = it.text,
                                uri = it.uri,
                                mimeType = it.mimeType
                            )
                        is io.modelcontextprotocol.kotlin.sdk.BlobResourceContents ->
                            ResourceContent(
                                type = "blob",
                                text = null,
                                uri = it.uri,
                                mimeType = it.mimeType
                            )
                        else -> null
                    }
                }
            )
        } catch (e: Exception) {
            ResourceContentResponse(false, "Failed to read resource: ${e.message}", null)
        }
    }

    /**
     * 列出指定服务器的工具
     */
    suspend fun listTools(serverName: String) = toolService.listTools(serverName)

    /**
     * 调用指定服务器的工具
     */
    suspend fun callTool(serverName: String, toolName: String, arguments: Map<String, Any>) =
        toolService.callTool(serverName, toolName, arguments)

    /**
     * 列出指定服务器的提示
     */
    suspend fun listPrompts(serverName: String) = toolService.listPrompts(serverName)

    /**
     * 获取指定提示
     */
    suspend fun getPrompt(serverName: String, promptName: String, arguments: Map<String, String> = emptyMap()) =
        toolService.getPrompt(serverName, promptName, arguments)
}

// API 响应数据类
@Serializable
data class ApiResponse(
    val success: Boolean,
    val message: String
)

@Serializable
data class ServerStatusResponse(
    val name: String,
    val status: String,
    val command: String,
    val args: List<String>,
    val disabled: Boolean,
    val lastError: String? = null,
    val startTime: Long? = null
)

@Serializable
data class ResourceListResponse(
    val success: Boolean,
    val message: String,
    val resources: List<ResourceInfo>
)

@Serializable
data class ResourceInfo(
    val uri: String,
    val name: String,
    val description: String? = null,
    val mimeType: String? = null
)

@Serializable
data class ResourceContentResponse(
    val success: Boolean,
    val message: String,
    val content: ResourceContent? = null
)

@Serializable
data class ResourceContent(
    val type: String,
    val text: String? = null,
    val uri: String,
    val mimeType: String? = null
)
