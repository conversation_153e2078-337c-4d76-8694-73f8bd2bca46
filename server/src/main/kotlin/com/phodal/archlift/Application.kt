package com.phodal.archlift

import com.phodal.archlift.mcp.api.McpApiService
import com.phodal.archlift.mcp.api.mcpRoutes
import com.phodal.archlift.mcp.client.McpClientManager
import com.phodal.archlift.mcp.client.McpToolService
import com.phodal.archlift.mcp.config.McpConfigManager
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.engine.*
import io.ktor.server.netty.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json

fun main() {
    embeddedServer(Netty, port = SERVER_PORT, host = "0.0.0.0", module = Application::module)
        .start(wait = true)
}

fun Application.module() {
    // 配置 JSON 序列化
    install(ContentNegotiation) {
        json(Json {
            prettyPrint = true
            isLenient = true
            ignoreUnknownKeys = true
        })
    }

    // 初始化 MCP 组件
    val configManager = McpConfigManager()
    val clientManager = McpClientManager(configManager)
    val toolService = McpToolService(clientManager)
    val apiService = McpApiService(configManager, clientManager, toolService)

    // 加载配置并启动服务器
    launch {
        configManager.loadConfig()
        clientManager.startAllServers()
    }

    // 注册关闭钩子
    environment.monitor.subscribe(ApplicationStopping) {
        clientManager.cleanup()
    }

    routing {
        get("/") {
            call.respondText("ArchLift MCP Client: ${Greeting().greet()}")
        }

        // MCP API 路由
        mcpRoutes(apiService)
    }
}