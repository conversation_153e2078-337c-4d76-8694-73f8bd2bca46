package com.phodal.archlift.mcp

import com.phodal.archlift.mcp.config.McpConfigManager
import com.phodal.archlift.mcp.client.McpClientManager
import com.phodal.archlift.mcp.client.McpToolService
import com.phodal.archlift.mcp.api.McpApiService
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import java.nio.file.Paths

/**
 * MCP 集成测试
 */
class McpIntegrationTest {

    @Test
    fun testConfigManagerLoading() {
        val configManager = McpConfigManager(Paths.get("mcp-config.json"))
        val config = configManager.loadConfig()
        
        assertNotNull(config, "Config should be loaded successfully")
        assertTrue(config.mcpServers.isNotEmpty(), "Should have at least one server configured")
        assertTrue(config.mcpServers.containsKey("java-parser"), "Should contain java-parser server")
    }

    @Test
    fun testServerStatusRetrieval() = runBlocking {
        val configManager = McpConfigManager(Paths.get("mcp-config.json"))
        val clientManager = McpClientManager(configManager)
        val toolService = McpToolService(clientManager)
        val apiService = McpApiService(configManager, clientManager, toolService)
        
        configManager.loadConfig()
        
        val statuses = apiService.getServerStatuses()
        assertTrue(statuses.isNotEmpty(), "Should have server statuses")
        
        val javaParserStatus = statuses.find { it.name == "java-parser" }
        assertNotNull(javaParserStatus, "Should have java-parser status")
    }

    @Test
    fun testJavaParserConfiguration() {
        val configManager = McpConfigManager(Paths.get("mcp-config.json"))
        configManager.loadConfig()
        
        val javaParserServer = configManager.getServer("java-parser")
        assertNotNull(javaParserServer, "Java parser server should be configured")
        assertTrue(javaParserServer.config.command == "java", "Command should be java")
        assertTrue(javaParserServer.config.args.contains("-jar"), "Should contain -jar argument")
        assertTrue(!javaParserServer.config.disabled, "Java parser should be enabled")
    }

    @Test
    fun testConfigReloading() = runBlocking {
        val configManager = McpConfigManager(Paths.get("mcp-config.json"))
        val clientManager = McpClientManager(configManager)
        val toolService = McpToolService(clientManager)
        val apiService = McpApiService(configManager, clientManager, toolService)
        
        // Initial load
        configManager.loadConfig()
        val initialServers = configManager.servers.size
        
        // Reload
        val reloadResponse = apiService.reloadConfig()
        assertTrue(reloadResponse.success, "Config reload should succeed")
        
        val reloadedServers = configManager.servers.size
        assertTrue(reloadedServers == initialServers, "Server count should remain the same after reload")
    }
}
