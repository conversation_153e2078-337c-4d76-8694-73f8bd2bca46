# ArchLift MCP Client & Java Parser Server

这个项目将 ArchLift 的 server 模块改造为一个 MCP (Model Context Protocol) Client，并在 `/mcps` 目录下提供了一个 Java Parser MCP Server。

## 架构概述

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Client    │    │  Java Parser     │    │  Other MCP      │
│   (Server)      │◄──►│  MCP Server      │    │  Servers        │
│                 │    │  (mcps/java-     │    │                 │
│                 │    │   parser)        │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐
│   REST API      │
│   Endpoints     │
└─────────────────┘
```

## 功能特性

### MCP Client (Server 模块)
- **配置管理**: 支持 JSON 配置文件动态管理 MCP 服务器
- **连接管理**: 自动启动、停止和重连 MCP 服务器
- **REST API**: 提供完整的 REST API 接口
- **工具调用**: 支持调用 MCP 服务器的工具和提示
- **资源访问**: 支持读取 MCP 服务器的资源

### Java Parser MCP Server
- **代码解析**: 解析 Java 源代码并提取结构信息
- **项目分析**: 分析整个 Java 项目的结构
- **AST 提取**: 提取类、方法、字段等详细信息

## 配置文件

MCP 配置文件 (`mcp-config.json`) 示例：

```json
{
  "mcpServers": {
    "java-parser": {
      "command": "java",
      "args": ["-jar", "mcps/java-parser/build/libs/java-parser-1.0.0.jar"],
      "disabled": false,
      "autoApprove": ["parse_java_file", "parse_java_code", "analyze_java_project"],
      "workingDirectory": ".",
      "timeout": 30000
    }
  }
}
```

### 配置参数说明

- `command`: 启动 MCP 服务器的命令
- `args`: 命令行参数
- `env`: 环境变量 (可选)
- `disabled`: 是否禁用此服务器
- `autoApprove`: 自动批准的工具列表
- `workingDirectory`: 工作目录 (可选)
- `timeout`: 超时时间 (毫秒)

## 快速开始

### 1. 构建项目

```bash
# 构建整个项目
./gradlew build

# 构建 Java Parser MCP Server
./gradlew :mcps:java-parser:build
```

### 2. 启动 MCP Client

```bash
# 启动 MCP Client (Server 模块)
./gradlew :server:run
```

服务器将在 `http://localhost:8080` 启动。

### 3. 测试 API

#### 获取所有服务器状态
```bash
curl http://localhost:8080/api/mcp/servers
```

#### 启动 Java Parser 服务器
```bash
curl -X POST http://localhost:8080/api/mcp/servers/java-parser/start
```

#### 列出 Java Parser 的工具
```bash
curl http://localhost:8080/api/mcp/servers/java-parser/tools
```

#### 调用 Java 代码解析工具
```bash
curl -X POST http://localhost:8080/api/mcp/servers/java-parser/tools/parse_java_code \
  -H "Content-Type: application/json" \
  -d '{
    "arguments": {
      "code": "public class Test { private String name; public String getName() { return name; } }"
    }
  }'
```

#### 解析 Java 文件
```bash
curl -X POST http://localhost:8080/api/mcp/servers/java-parser/tools/parse_java_file \
  -H "Content-Type: application/json" \
  -d '{
    "arguments": {
      "file_path": "test-files/Example.java"
    }
  }'
```

## API 接口文档

### 服务器管理

- `GET /api/mcp/servers` - 获取所有服务器状态
- `GET /api/mcp/servers/{name}` - 获取指定服务器状态
- `POST /api/mcp/servers/{name}/start` - 启动服务器
- `POST /api/mcp/servers/{name}/stop` - 停止服务器
- `POST /api/mcp/config/reload` - 重新加载配置

### 资源管理

- `GET /api/mcp/servers/{name}/resources` - 列出服务器资源
- `GET /api/mcp/servers/{name}/resources/{uri}` - 读取指定资源

### 工具调用

- `GET /api/mcp/servers/{name}/tools` - 列出服务器工具
- `POST /api/mcp/servers/{name}/tools/{toolName}` - 调用指定工具

### 提示管理

- `GET /api/mcp/servers/{name}/prompts` - 列出服务器提示
- `POST /api/mcp/servers/{name}/prompts/{promptName}` - 获取指定提示

### 健康检查

- `GET /api/mcp/health` - 健康检查

## Java Parser 工具

Java Parser MCP Server 提供以下工具：

### 1. parse_java_file
解析指定的 Java 文件

**参数:**
- `file_path` (string): Java 文件路径

### 2. parse_java_code
解析 Java 代码字符串

**参数:**
- `code` (string): Java 源代码

### 3. analyze_java_project
分析 Java 项目结构

**参数:**
- `project_path` (string): 项目根目录路径

## 测试

运行测试：

```bash
# 运行所有测试
./gradlew test

# 运行 MCP 集成测试
./gradlew :server:test --tests "*McpIntegrationTest*"
```

## 扩展开发

### 添加新的 MCP Server

1. 在 `/mcps` 目录下创建新的子项目
2. 使用 MCP Kotlin SDK 实现服务器逻辑
3. 在 `settings.gradle.kts` 中添加项目引用
4. 在 `mcp-config.json` 中添加服务器配置

### 自定义配置管理

可以通过继承 `McpConfigManager` 类来实现自定义的配置管理逻辑，例如从数据库或远程服务加载配置。

## 故障排除

### 常见问题

1. **服务器启动失败**: 检查配置文件中的命令和参数是否正确
2. **连接超时**: 增加配置中的 `timeout` 值
3. **工具调用失败**: 确保 MCP 服务器正在运行且工具名称正确

### 日志查看

服务器日志会输出到控制台，包含详细的错误信息和调试信息。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。
