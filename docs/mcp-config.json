{"mcpServers": {"java-parser": {"command": "java", "args": ["-jar", "mcps/java-parser/build/libs/java-parser-1.0.0.jar"], "disabled": false, "autoApprove": ["parse_java_file", "parse_java_code", "analyze_java_project"], "workingDirectory": ".", "timeout": 30000}, "example-node-server": {"command": "npx", "args": ["-y", "@jetbrains/mcp-proxy"], "disabled": true, "autoApprove": [], "env": {"NODE_ENV": "production"}, "timeout": 30000}}}