#!/bin/bash

# ArchLift MCP System 启动脚本

set -e

echo "🚀 Starting ArchLift MCP System..."

# 检查是否在项目根目录
if [ ! -f "build.gradle.kts" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# 构建项目
echo "📦 Building project..."
./gradlew build -x test

# 构建 Java Parser MCP Server
echo "📦 Building Java Parser MCP Server..."
./gradlew :mcps:java-parser:build

# 检查配置文件
if [ ! -f "mcp-config.json" ]; then
    echo "⚠️  Warning: mcp-config.json not found, creating default config..."
    cat > mcp-config.json << 'EOF'
{
  "mcpServers": {
    "java-parser": {
      "command": "java",
      "args": [
        "-jar",
        "mcps/java-parser/build/libs/java-parser-1.0.0.jar"
      ],
      "disabled": false,
      "autoApprove": [
        "parse_java_file",
        "parse_java_code",
        "analyze_java_project"
      ],
      "workingDirectory": ".",
      "timeout": 30000
    }
  }
}
EOF
fi

# 创建测试文件目录
mkdir -p test-files

# 启动 MCP Client
echo "🌟 Starting MCP Client..."
echo "📍 Server will be available at: http://localhost:8080"
echo "📖 API Documentation: http://localhost:8080/api/mcp/health"
echo ""
echo "🔧 Useful API endpoints:"
echo "  - GET  /api/mcp/servers                     - List all servers"
echo "  - POST /api/mcp/servers/java-parser/start   - Start Java Parser"
echo "  - GET  /api/mcp/servers/java-parser/tools   - List Java Parser tools"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

./gradlew :server:run
